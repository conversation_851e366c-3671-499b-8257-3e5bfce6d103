// Importation de la classe AppController depuis le fichier AppController.js
import AppController from "./AppController.js";
// Importation de la classe UIController depuis le fichier UIController.js
import UIController from "./UIController.js";

// Définition de la classe Controller
class Controller {
  // Constructeur de la classe Controller
  constructor() {
    // Création d'une instance de UIController
    this.UIController = new UIController();
    // Récupération des données de notes depuis le localStorage, ou initialisation à un tableau vide
    this.getData = JSON.parse(localStorage.getItem("noteData")) || [];
    // Fonction pour sauvegarder les données de notes dans le localStorage
    this.setData = (data) =>
      localStorage.setItem("noteData", JSON.stringify(data));
    // Appel des différentes fonctions pour gérer l'application
    this.openForm();
    this.createNoteData();
    this.createNoteUI();
    this.deleteNotes();
    this.editNote();
  }

  // Fonction pour ouvrir le formulaire de création de note
  openForm() {
    // Récupération de l'élément newNote depuis UIController
    const { newNote } = this.UIController;

    // Fonction pour gérer l'ouverture du formulaire
    const openFormFn = (e) => {
      e.preventDefault(); // Empêche le comportement par défaut de l'événement
      newNote.classList.add("active"); // Ajoute la classe 'active' pour afficher le formulaire
      // Ajoute un écouteur d'événement pour fermer le formulaire si on clique en dehors
      window.addEventListener("click", (e) => {
        !e.target.closest(".new-note") && newNote.classList.remove("active");
      });
    };
    // Ajout d'écouteurs d'événements pour ouvrir le formulaire au clic ou au clic droit
    newNote.addEventListener("click", openFormFn);
    newNote.addEventListener("contextmenu", openFormFn);
  }

  // Fonction pour créer les données de note
  createNoteData() {
    // Récupération des éléments nécessaires depuis UIController
    const { newNote, noteFiles, noteTitle, noteText, noteBtn, createNoteHTML } =
      this.UIController;

    // Ajout d'un écouteur d'événements pour le bouton de création de note
    noteBtn.addEventListener("click", (e) => {
      e.preventDefault(); // Empêche le comportement par défaut de l'événement
      e.stopPropagation(); // Empêche la propagation de l'événement

      // Récupération de la date actuelle au format 'en-GB'
      const currentDate = new Date().toLocaleDateString("en-GB");
      // Vérification que le titre et le texte de la note ne sont pas vides
      if (noteTitle.value.trim() !== "" && noteText.value.trim() !== "") {
        // Création d'une nouvelle note avec AppController
        const note = new AppController(
          noteTitle.value,
          noteText.value,
          currentDate
        );

        // Ajout de la note au tableau de données
        this.getData.push(note);
        // Sauvegarde des données mises à jour dans le localStorage
        this.setData(this.getData);
        // Création de l'HTML pour la note
        createNoteHTML(note, noteFiles);
        // Réinitialisation des écouteurs pour la suppression et l'édition des notes
        this.deleteNotes();
        this.editNote();

        // Fermeture du formulaire et réinitialisation des champs de saisie
        newNote.classList.remove("active");
        noteTitle.value = "";
        noteText.value = "";
      }
    });

    // Ajout d'un écouteur d'événements pour permettre la création de note avec la touche 'Enter'
    noteText.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        noteBtn.click(); // Simule un clic sur le bouton de création de note
      }
    });
  }

  // Fonction pour créer l'interface utilisateur des notes
  createNoteUI() {
    // Récupération de la fonction de création d'HTML et du conteneur de notes
    const { createNoteHTML, noteFiles } = this.UIController;

    // Parcours des données de notes et création de l'interface pour chacune
    this.getData.forEach((noteData) => {
      createNoteHTML(noteData, noteFiles);
    });
  }

  // Fonction pour gérer la suppression des notes
  deleteNotes() {
    // Récupération des éléments de notes et des boutons de suppression depuis UIController
    const notes = this.UIController.notes();
    const deleteBtns = this.UIController.deleteBtn();
    const { noteFiles } = this.UIController; // Récupération du conteneur où les notes sont affichées

    // Parcours de chaque bouton de suppression
    deleteBtns.forEach((btn, index) => {
      // Ajout d'un écouteur d'événements pour le clic sur le bouton de suppression
      btn.onclick = () => {
        // Suppression de l'élément de note correspondant du DOM
        noteFiles.removeChild(notes[index]);
        // Suppression de la note du tableau de données
        this.getData.splice(index, 1);
        // Sauvegarde des données mises à jour dans le localStorage
        this.setData(this.getData);
      };
    });
  }

  // Fonction pour gérer l'édition des notes
  editNote() {
    // Récupération des éléments de notes depuis UIController
    const notes = this.UIController.notes();

    // Parcours de chaque note pour ajouter des écouteurs d'événements
    notes.forEach((note, index) => {
      // Récupération des éléments de titre et de texte de la note
      const noteTitle = note.querySelector(".title");
      const noteText = note.querySelector(".note-text");

      // Ajout d'un écouteur d'événements pour l'événement 'blur' sur le titre
      noteTitle.addEventListener("blur", () => {
        // Mise à jour du titre de la note dans le tableau de données
        this.getData[index].title = noteTitle.textContent;
        // Sauvegarde des données mises à jour dans le localStorage
        this.setData(this.getData);
      });
      // Ajout d'un écouteur d'événements pour l'événement 'blur' sur le texte de la note
      noteText.addEventListener("blur", () => {
        // Mise à jour du texte de la note dans le tableau de données
        this.getData[index].note = noteText.textContent;
        // Sauvegarde des données mises à jour dans le localStorage
        this.setData(this.getData);
      });
    });
  }
}

export default Controller;
