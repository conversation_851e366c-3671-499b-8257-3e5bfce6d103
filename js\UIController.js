/**
 * Classe représentant le contrôleur de l'interface utilisateur pour la gestion des notes.
 */
class UIController {
  /**
   * Crée une instance de UIController et initialise les éléments de l'interface utilisateur.
   */
  constructor() {
    /**
     * Fonction qui retourne tous les éléments de notes.
     * @returns {HTMLElement[]} Un tableau d'éléments HTML représentant les notes.
     */
    this.notes = () => [...document.querySelectorAll(".note")];

    /**
     * Fonction qui retourne tous les boutons de suppression.
     * @returns {HTMLElement[]} Un tableau d'éléments HTML représentant les boutons de suppression.
     */
    this.deleteBtn = () => [...document.querySelectorAll(".delete-btn")];

    // Éléments de l'interface utilisateur
    this.newNote = document.querySelector(".new-note");
    this.noteFiles = document.querySelector(".note-files");
    this.noteForm = document.querySelector(".note-form");
    this.noteTitle = document.querySelector(".title-input");
    this.noteText = document.querySelector(".note-text-input");
    this.noteBtn = document.querySelector(".note-btn");
  }

  /**
   * Crée le HTML d'une note et l'ajoute à l'élément spécifié.
   *
   * @param {Object} noteData - Les données de la note.
   * @param {string} noteData.title - Le titre de la note.
   * @param {string} noteData.note - Le contenu de la note.
   * @param {string} noteData.time - La date et l'heure de la note.
   * @param {HTMLElement} noteFilesEl - L'élément HTML où la note sera ajoutée.
   */
  createNoteHTML(noteData, noteFilesEl) {
    const noteHTML = ` <div class="note">
          <h2 class="title" contenteditable="true">${noteData.title}</h2>
          <p class="note-text" contenteditable="true">${noteData.note}</p>
          <div class="settings">
            <span class="date">${noteData.time}</span>
            <a href="http:#" class="delete-btn"><i class="bx bxs-trash"></i></a>
          </div>
        </div>`;

    noteFilesEl.insertAdjacentHTML("beforeend", noteHTML);
  }
}

export default UIController;
