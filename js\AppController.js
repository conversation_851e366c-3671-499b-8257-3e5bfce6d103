/**
 * Classe représentant un contrôleur d'application pour les notes.
 */
class AppController {
  /**
   * Crée une instance d'AppController.
   *
   * @param {string} title - Le titre de la note.
   * @param {string} note - Le contenu de la note.
   * @param {Date} time - L'heure à laquelle la note a été créée ou modifiée.
   */
  constructor(title, note, time) {
    this.title = title;
    this.note = note;
    this.time = time;
  }
}

export default AppController;
